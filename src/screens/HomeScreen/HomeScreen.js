import React, { useState, useCallback, useEffect, useMemo } from "react";
import {
  View,
  StyleSheet,
  TouchableOpacity,
  Image,
  ScrollView,
} from "react-native";
import { useDispatch, useSelector } from "react-redux";
import Header from "../../components/Header";
import SearchBar from "../../components/SearchBar";
import NoContactsImported from "./components/NoContactsImported";
import { PrimaryButton } from "../../components/Button";
import AppLoader from "../../components/AppLoader";
import MyText from "../../components/MyText";
import icons from "../../assets/icons";
import colors from "../../assets/colors";
import commonStyles from "../../assets/commonStyles";
import { getProfile } from "../../redux/features/authSlice";
import { getContacts, getDuplicates } from "../../redux/features/contactSlice";
import { useFocusEffect, useNavigation } from "@react-navigation/native";
import ContactList from "../../components/ContactList";
import { groupContactsByAlphabet } from "../../utils/commonHelpers";
import { fetchContactsFromSource } from "../../utils/contactHelpers";
import FindingDuplicates from "./components/FindingDuplicates";
import AppModal from "../../components/AppModal";
import {
  getPersonalProfile,
  getProfileNameData,
} from "../../redux/features/SharingProfileSlice";
import { storage } from "../../utils/storage";
import SortOptionsBox from "../../components/SortOptionsBox";
import ChipSelector from "../../components/ChipSelector";

import {
  filterOptions,
  importOptions,
  sortOptions,
} from "../../utils/constants";
import BottomModal from "../../components/BottomModal";
import FloatingPlusButton from "../../components/FloatingPlusButton";
import { getTags, setShowModal } from "../../redux/features/mainSlice";
import MyCard from "./components/MyCard";
import AsyncStorage from "@react-native-async-storage/async-storage";

// --- Helper Components ---
const ProfileTagChips = ({
  activeFilter,
  profileOptions,
  tagOptions,
  selectedProfile,
  setSelectedProfile,
  selectedTag,
  setSelectedTag,
}) => {
  if (activeFilter === "profile" && profileOptions.length === 0) {
    return (
      <View style={{ paddingHorizontal: 20, marginTop: 10 }}>
        <MyText p children={"No Profiles Created"} />
      </View>
    );
  }
  if (activeFilter === "tag" && tagOptions.length === 0) {
    return (
      <View style={{ paddingHorizontal: 20, marginTop: 10 }}>
        <MyText p children={"No Tags Created"} />
      </View>
    );
  }
  if (activeFilter === "profile" && profileOptions.length > 0) {
    return (
      <View style={{ paddingHorizontal: 20 }}>
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          <ChipSelector
            options={profileOptions}
            selectedValue={selectedProfile}
            onSelect={(value) => {
              setSelectedProfile(value === selectedProfile ? null : value);
            }}
            containerStyle={{ width: "100%", gap: 10 }}
          />
        </ScrollView>
      </View>
    );
  }
  if (activeFilter === "tag" && tagOptions.length > 0) {
    return (
      <View style={{ paddingHorizontal: 20 }}>
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          <ChipSelector
            options={tagOptions}
            selectedValue={selectedTag}
            onSelect={(value) => {
              setSelectedTag(value === selectedTag ? null : value);
            }}
            containerStyle={{ width: "100%", gap: 10 }}
          />
        </ScrollView>
      </View>
    );
  }
  return null;
};

const ImportOptionsList = React.memo(
  ({ importOptions, handleFetchContacts }) => (
    <>
      {importOptions.map((option, index) => (
        <TouchableOpacity
          key={index}
          onPress={() => handleFetchContacts(option.action)}
          style={{ marginVertical: 10 }}
        >
          <View style={[commonStyles.row, styles.modalOption]}>
            <View style={commonStyles.rowWithoutSpaceBetween}>
              <Image
                source={option.icon}
                style={commonStyles.smallIcon}
                resizeMode="contain"
              />
              <MyText
                h6
                center
                children={option.name}
                style={styles.optionText}
              />
            </View>
            <Image
              source={icons.rightArrowIcon}
              style={commonStyles.extraSmallIcon}
              resizeMode="contain"
            />
          </View>
        </TouchableOpacity>
      ))}
    </>
  )
);

const HomeScreen = () => {
  // --- Navigation & Redux ---
  const navigation = useNavigation();
  const dispatch = useDispatch();

  // --- State ---
  const [isModalVisible, setModalVisible] = useState(false);
  const [showProfileModal, setShowProfileModal] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [isSortBoxVisible, setSortBoxVisible] = useState(false);
  const [isSecondBoxVisible, setSecondBoxVisible] = useState(false);
  const [selectedProfile, setSelectedProfile] = useState(null);
  const [selectedTag, setSelectedTag] = useState(null);
  const [activeFilter, setActiveFilter] = useState(null);
  const [activeSort, setActiveSort] = useState("az");
  const [prevContactsLength, setPrevContactsLength] = useState(null);

  // const showModal = useSelector(
  //   (state) => state.mainSlice.showModal
  // );

  // --- Selectors ---
  const user = useSelector((state) => state.auth.user);
  const contacts = useSelector((state) => state.contactSlice.getContacts);
  // console.log("🚀 ~ HomeScreen ~ contacts:", JSON.stringify(contacts, null, 2));
  const duplicatesState = useSelector(
    (state) => state.contactSlice.getDuplicatesState
  );
  const tagsData = useSelector((state) => state.mainSlice.tagsData);
  const personalProfileState = useSelector(
    (state) => state.sharingProfileSlice.getPersonalProfile
  );
  const getProfileNameDataState = useSelector(
    (state) => state.sharingProfileSlice.getProfileNameData
  );
  const { data: personalProfiledata } = personalProfileState;
  const { data: allProfileNameData } = getProfileNameDataState;
  const isPersonalProfileComplete =
    personalProfiledata?.data?.userInfo?.is_profile_complete;

  // --- Memoized Options ---
  const profileOptions = useMemo(() => {
    if (allProfileNameData?.data?.result?.length) {
      return allProfileNameData.data.result.map((item) => ({
        label: item.profile_name,
        value: item.profile_name,
        contacts: item.contacts,
      }));
    }
    return [];
  }, [allProfileNameData]);

  const tagOptions = useMemo(() => {
    if (tagsData && Array.isArray(tagsData.tags)) {
      return tagsData.tags.map((tag) => ({
        label: tag.tag_name,
        value: tag.tag_name,
        contacts: tag.contacts || tag.members || [],
      }));
    } else if (tagsData && Array.isArray(tagsData.result)) {
      return tagsData.result.map((tag) => ({
        label: tag.tag_name,
        value: tag.tag_name,
        contacts: tag.contacts || tag.members || [],
      }));
    }
    return [];
  }, [tagsData]);

  // --- Derived Contacts ---
  const profileContacts = useMemo(() => {
    if (selectedProfile) {
      const found = profileOptions.find((opt) => opt.value === selectedProfile);
      return found ? found.contacts : [];
    }
    return [];
  }, [selectedProfile, profileOptions]);

  const tagContacts = useMemo(() => {
    if (selectedTag) {
      const found = tagOptions.find((opt) => opt.value === selectedTag);
      return found ? found.contacts : [];
    }
    return [];
  }, [selectedTag, tagOptions]);

  const filteredContacts = useMemo(() => {
    let baseContacts;
    if (activeFilter === "profile" && selectedProfile) {
      baseContacts = profileContacts;
    } else if (activeFilter === "tag" && selectedTag) {
      baseContacts = tagContacts;
    } else if (activeFilter === "favourites") {
      // Only show contacts where is_favorite is true
      baseContacts = (contacts?.data?.data?.result || []).filter(
        (contact) => contact.is_favorite === true
      );
    } else {
      baseContacts = contacts?.data?.data?.result || [];
    }
    if (!searchQuery) {
      return baseContacts;
    } else if (baseContacts.length) {
      const search = searchQuery.trim().toLowerCase();
      const filtered = baseContacts.filter((contact) => {
        const name = `${contact.firstName || ""} ${contact.middleName || ""} ${
          contact.lastName || ""
        }`.toLowerCase();
        const emails = Array.isArray(contact.emails)
          ? contact.emails.map((e) => e.address?.toLowerCase() || "").join(" ")
          : "";
        const phones = Array.isArray(contact.phoneNumbers)
          ? contact.phoneNumbers
              .map((p) => p.number?.toLowerCase() || "")
              .join(" ")
          : "";
        return (
          name.includes(search) ||
          emails.includes(search) ||
          phones.includes(search)
        );
      });
      return filtered;
    }
    return [];
  }, [
    contacts,
    searchQuery,
    selectedProfile,
    profileContacts,
    activeFilter,
    selectedTag,
    tagContacts,
    activeSort,
  ]);

  // --- Handlers ---
  const handleSearchChange = (text) => setSearchQuery(text);

  const handleSortIconPress = () => {
    setSecondBoxVisible(false);
    setSortBoxVisible((prev) => !prev);
  };

  const handleSortOption = (option) => {
    setSortBoxVisible(false);
    if (!option) {
      setActiveSort(null);
      dispatch(getContacts());
      return;
    }
    setActiveSort(option.value);
    let params = {};
    if (option.value === "recent") {
      params = { sort: -1 };
    }
    dispatch(getContacts(params));
  };

  const handleSecondRightIconPress = () => {
    setSortBoxVisible(false);
    setSecondBoxVisible((prev) => !prev);
  };

  const handleSecondOption = (option) => {
    setSecondBoxVisible(false);
    if (!option) {
      setActiveFilter(null);
      setSelectedProfile(null);
      setSelectedTag(null);
      dispatch(getContacts());
      return;
    }
    const value = option.value?.toLowerCase();
    if (value === "profile") {
      if (activeFilter === "profile") {
        setActiveFilter(null);
        setSelectedProfile(null);
        dispatch(getContacts());
      } else {
        setActiveFilter("profile");
      }
    } else if (value === "city") {
      setActiveFilter("city");
      dispatch(getContacts({ filter: "city" }));
    } else if (value === "favourites") {
      setActiveFilter("favourites");
      dispatch(getContacts({ isFavourite: true }));
    } else if (value === "tag") {
      if (activeFilter === "tag") {
        setActiveFilter(null);
        setSelectedTag(null);
        dispatch(getContacts());
      } else {
        setActiveFilter("tag");
      }
    } else {
      setActiveFilter(option.value || null);
      setSelectedProfile(null);
      setSelectedTag(null);
      dispatch(getContacts());
    }
  };

  const handleAddManualContact = () => {
    navigation.navigate("AddManualContactScreen");
  };

  const handleProfileModalSubmit = async () => {
    setShowProfileModal(false);
    // storage.set(`profileModalSubmitted_${user?.id}`, "true");
    await AsyncStorage.setItem(`profileModalSubmitted_${user?.id}`, "true");
    // dispatch(setShowModal(user?.id));
    setTimeout(() => {
      navigation.navigate("ProfileCompletionScreen", { profileId: user?._id });
    }, 100);
  };

  const handleFetchContacts = useCallback(
    async (source) => {
      setModalVisible(false);
      const onSuccess = (contactsData, sourceType) => {
        navigation.navigate("ImportContactScreen", {
          contacts: contactsData,
          source: sourceType,
        });
      };
      const onError = (error) => {
        console.error("Error fetching contacts:", error);
      };
      await fetchContactsFromSource(source, onSuccess, onError, setIsLoading);
    },
    [navigation]
  );

  // --- Effects ---
  useEffect(() => {
    dispatch(getProfile());
    dispatch(getContacts());
    dispatch(getPersonalProfile());
    dispatch(getProfileNameData());
    dispatch(getTags());
  }, [dispatch]);
  useFocusEffect(
    useCallback(() => {
      dispatch(getProfile());
      dispatch(getContacts());
      dispatch(getPersonalProfile());
      dispatch(getProfileNameData());
      dispatch(getTags());
    }, [dispatch])
  );

  useEffect(() => {
    const checkProfileModal = async () => {
      const submitted = await AsyncStorage.getItem(
        `profileModalSubmitted_${user?.id}`
      );
      const hasSubmitted = submitted === "true";

      console.log(
        "🚀 ~ HomeScreen ~ checkProfileModal ~ submitted:",
        submitted
      );

      console.log(
        "🚀 ~ HomeScreen ~ checkProfileModal ~ hasSubmitted:",
        hasSubmitted
      );

      console.log(
        "🚀 ~ HomeScreen ~ checkProfileModal ~ isPersonalProfileComplete:",
        isPersonalProfileComplete
      );

      if (user && !isPersonalProfileComplete && !hasSubmitted) {
        setShowProfileModal(true);
      }
    };

    checkProfileModal();
  }, [user, isPersonalProfileComplete]);

  useEffect(() => {
    const currentLength = contacts?.data?.data?.result?.length || 0;
    if (prevContactsLength === null) {
      setPrevContactsLength(currentLength);
      if (currentLength > 0) {
        dispatch(getDuplicates());
      }
    } else if (currentLength !== prevContactsLength) {
      setPrevContactsLength(currentLength);
      if (currentLength > 0) {
        dispatch(getDuplicates());
      }
    }
  }, [contacts, dispatch, prevContactsLength]);

  // --- Render ---
  return (
    <View style={styles.container}>
      {/* Header Section */}
      <View style={styles.headerSection}>
        <Header
          title={`Welcome\nBack, ${user?.firstName}`}
          leftIcon={icons?.DrawerIcon}
          onPressLeft={() => navigation.openDrawer()}
          onPressRight2={() =>
            navigation.navigate("ContactDetailsScreen", {
              id: user?._id,
              personal: true,
            })
          }
          rightIcon1={icons?.notificationIcon}
          isAvatar
          pb={50}
        />
        <View style={styles.searchBarContainer}>
          <SearchBar
            placeholder="Search here"
            value={searchQuery}
            onChangeText={handleSearchChange}
            rightIcon={icons.filterIcon}
            onPressRightIcon1={handleSortIconPress}
            onPressRightIcon2={handleSecondRightIconPress}
          />
          {isSortBoxVisible && (
            <SortOptionsBox
              options={sortOptions}
              onSelect={handleSortOption}
              style={[styles.sortBoxOverlay, { right: 60 }]}
              optionStyle={styles.sortBoxOption}
              optionTextStyle={styles.optionText}
              activeValue={activeSort}
              allowDeselect={true}
            />
          )}
          {isSecondBoxVisible && (
            <SortOptionsBox
              options={filterOptions}
              onSelect={handleSecondOption}
              style={[styles.sortBoxOverlay, { right: 20 }]}
              optionStyle={styles.sortBoxOption}
              optionTextStyle={styles.optionText}
              activeValue={activeFilter}
              allowDeselect={true}
            />
          )}
        </View>
      </View>
      {/* Profile Modal */}
      <AppModal
        visible={showProfileModal}
        title="Complete Your Profile"
        description="Please complete your profile information to get started."
        btnTitle="Continue"
        onSubmit={handleProfileModalSubmit}
        onClose={() => setShowProfileModal(false)}
        containerStyle={{ zIndex: 2000 }}
      />
      {/* Loader */}
      <AppLoader isLoading={isLoading || contacts.loading} />
      {/* Duplicates Section */}
      <View style={{ marginTop: 22 }}>
        <FindingDuplicates
          isLoading={duplicatesState?.loading}
          duplicatesCount={
            duplicatesState?.data?.data?.totalDuplicateGroups ?? 0
          }
          onViewPress={() => {
            navigation.navigate("DuplicateContactScreen", {
              onRefetch: () => {
                dispatch(getContacts());
                dispatch(getDuplicates());
              },
            });
          }}
        />
      </View>
      {/* Profile/Tag Chips */}
      <ProfileTagChips
        activeFilter={activeFilter}
        profileOptions={profileOptions}
        tagOptions={tagOptions}
        selectedProfile={selectedProfile}
        setSelectedProfile={setSelectedProfile}
        selectedTag={selectedTag}
        setSelectedTag={setSelectedTag}
      />
      {/* Main Content */}
      <View style={styles.mainContentContainer}>
        <View style={styles.listContainer}>
          {filteredContacts.length > 0 ? (
            <>
              <MyText
                semibold
                h6
                children={
                  selectedProfile
                    ? `${selectedProfile} Contacts `
                    : `Contacts (${
                        contacts?.data?.data?.pagination?.total || 0
                      })`
                }
                style={styles.contactsHeader}
              />
              <MyCard
                name={user?.firstName}
                imageUri={user?.profileImage}
                onPress={() => navigation.navigate("MyCardScreen")}
              />
              <ContactList
                contacts={
                  activeSort === "az" || activeSort === "za"
                    ? groupContactsByAlphabet(filteredContacts, activeSort)
                    : filteredContacts
                }
                showAlphabetList={activeSort === "az" || activeSort === "za"}
                mode="viewprofile"
                reverseAlphabet={activeSort === "za"}
                ListHeaderComponent={<ProfileTagChips />}
              />
            </>
          ) : activeFilter &&
            ((activeFilter === "profile" && selectedProfile) ||
              (activeFilter === "tag" && selectedTag) ||
              activeFilter === "favourites") ? (
            <View style={{ alignItems: "center", marginTop: 40 }}>
              <MyText
                p
                children={
                  activeFilter === "favourites"
                    ? "No favourite contact is there."
                    : `No contacts found in the ${
                        activeFilter == "profile"
                          ? selectedProfile
                          : selectedTag
                      } ${activeFilter}.`
                }
              />
            </View>
          ) : (
            <>
              <NoContactsImported />
              <PrimaryButton
                onPress={() => setModalVisible(true)}
                title="Import Contacts"
                style={styles.button}
              />
            </>
          )}
        </View>
      </View>
      {/* Floating Add Button */}
      <FloatingPlusButton onPress={handleAddManualContact} />
      {/* Import Modal */}
      <BottomModal
        isVisible={isModalVisible}
        onClose={() => setModalVisible(false)}
        title="Import Contacts From"
      >
        <ImportOptionsList
          importOptions={importOptions}
          handleFetchContacts={handleFetchContacts}
        />
      </BottomModal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  headerSection: {
    position: "relative",
    zIndex: 10010,
  },
  mainContentContainer: {
    flex: 1,
    zIndex: 1,
  },
  searchBarContainer: {
    position: "absolute",
    bottom: -20,
    left: 0,
    right: 0,
    paddingHorizontal: 15,
    zIndex: 10020,
  },
  listContainer: {
    flex: 1,
    paddingTop: 15,
    zIndex: 1,
  },
  contactsHeader: {
    paddingHorizontal: 20,
    marginBottom: 10,
  },
  button: {
    backgroundColor: colors.black,
    width: "50%",
    marginTop: 10,
    alignSelf: "center",
  },
  modalOption: {},
  optionText: {
    alignSelf: "left",
  },
  sortBoxOverlay: {
    position: "absolute",
    top: 80,
    right: 10,
    backgroundColor: "#fff",
    borderRadius: 8,
    elevation: 20,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.18,
    shadowRadius: 6,
    zIndex: 20000,
    paddingVertical: 8,
    minWidth: 150,
  },
  sortBoxOption: {
    paddingVertical: 4,
    paddingHorizontal: 6,
  },
});

export default HomeScreen;
