import React, { useRef, useState, useEffect, useCallback } from "react";
import {
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  View,
  Alert,
} from "react-native";
import PropTypes from "prop-types";
import { useDispatch, useSelector } from "react-redux";
import {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
} from "react-native-reanimated";
import Header from "../../components/Header";
import icons from "../../assets/icons";
import { PrimaryButton } from "../../components/Button";
import { styles } from "./styles";
import { MAIN_SECTIONS, INITIAL_FORM_STATE } from "./constants/formConfig";
import PersonalSection from "./components/PersonalSection";
import SocialSection from "./components/SocialSection";
import BusinessSection from "./components/BusinessSection";
import TabNavigation from "./components/TabNavigation";
import {
  getAccessProfileFromProfileId,
  getPersonalProfile,
  updateBusinessDetailsSharingProfile,
  updateSharingProfile,
} from "../../redux/features/SharingProfileSlice";
import { mapProfileDataForApi } from "../../utils/profileDataMapper";
import { showToast } from "../../utils/toastConfig";
import ProfilePicture from "../../components/ProfilePicture";

const ProfileCompletionScreen = ({ navigation, route }) => {
  const { profileId, ownProfileData } = route?.params || {};
  const dispatch = useDispatch();
  const user = useSelector((state) => state.auth.user);
  const accessProfileFromProfileIdState = useSelector(
    (state) => state.sharingProfileSlice.getAccessProfileFromProfileId
  );
  const { data: accessProfileData } = accessProfileFromProfileIdState;

  // Refs
  const tabPosition = useSharedValue(0);
  const phoneInputRef = useRef(null);
  const scrollViewRef = useRef(null);
  const personalSectionRef = useRef(null);
  const socialSectionRef = useRef(null);
  const businessSectionRef = useRef(null);

  // State
  const [activeMainSection, setActiveMainSection] = useState("personal");
  const [form, setForm] = useState(INITIAL_FORM_STATE);
  const [errors, setErrors] = useState({});
  const [loading, setLoading] = useState(false);
  const [isTimezoneDropdownOpen, setIsTimezoneDropdownOpen] = useState(false);
  // New: additional emails and phones state
  const [additionalEmails, setAdditionalEmails] = useState([]);
  const [additionalPhones, setAdditionalPhones] = useState([]);

  // Fetch profile data on mount
  useEffect(() => {
    if (profileId) {
      dispatch(getAccessProfileFromProfileId(profileId));
    }
  }, [dispatch, profileId]);

  // Set form from profile data
  useEffect(() => {
    if (accessProfileFromProfileIdState.error) {
      showToast(
        "error",
        "There was an error loading your profile data. You can continue filling out the form, and we'll save your changes."
      );
      return;
    }
    if (accessProfileData?.data?.userInfo && !ownProfileData) {
      try {
        const userInfo = accessProfileData.data.userInfo;
        if (typeof userInfo !== "object")
          throw new Error("Invalid user info format");
        setForm((prevForm) => {
          const newForm = { ...prevForm };
          // Personal Details
          newForm.firstName = userInfo.firstName || "";
          newForm.middleName = userInfo.middleName || "";
          newForm.lastName = userInfo.lastName || "";
          newForm.email = userInfo.emails?.[0]?.address || "";
          newForm.phone = userInfo.phoneNumbers?.[0]?.number || "";
          newForm.dateOfBirth = userInfo.dateOfBirth || "";
          newForm.gender = userInfo.gender || "";
          newForm.nationality = userInfo.nationality || "";
          // Health Insurance
          newForm.policyNumber = userInfo.healthInsurance?.policyNumber || "";
          newForm.insuranceProvider =
            userInfo.healthInsurance?.insuranceProvider || "";
          newForm.policyPeriod =
            userInfo.healthInsurance?.policyPeriod?.toString() || "";
          newForm.effectiveDate = userInfo.healthInsurance?.effectiveDate || "";
          newForm.expirationDate =
            userInfo.healthInsurance?.expirationDate || "";
          newForm.sumInsured =
            userInfo.healthInsurance?.sumInsured?.toString() || "";
          // Address
          newForm.street = userInfo.addresses_home?.street || "";
          newForm.city = userInfo.addresses_home?.city || "";
          newForm.state = userInfo.addresses_home?.state || "";
          newForm.zipCode = userInfo.addresses_home?.zipCode || "";
          newForm.country = userInfo.addresses_home?.country || "";
          newForm.apartment = userInfo.addresses_home?.apartment || "";
          // Billing Address
          newForm.billingApartment = userInfo.billing_address?.apartment || "";
          newForm.billingStreet = userInfo.billing_address?.street || "";
          newForm.billingCity = userInfo.billing_address?.city || "";
          newForm.billingState = userInfo.billing_address?.state || "";
          newForm.billingZipCode = userInfo.billing_address?.zipCode || "";
          newForm.billingCountry = userInfo.billing_address?.country || "";
          // Card Details
          newForm.cardName = userInfo.card_details?.nameOnCard || "";
          newForm.cardNumber = userInfo.card_details?.cardNumber || "";
          newForm.cardExpiry = userInfo.card_details?.expiryDate || "";
          newForm.cardCvv = userInfo.card_details?.cvv || "";
          // Bank Account
          newForm.accountName = userInfo.account_details?.name || "";
          newForm.accountNumber = userInfo.account_details?.accountNumber || "";
          newForm.ifscCode = userInfo.account_details?.ifscCode || "";
          newForm.bankName = userInfo.account_details?.bank || "";
          // Other Address
          newForm.otherApartment = userInfo.addresses_other?.apartment || "";
          newForm.otherStreet = userInfo.addresses_other?.street || "";
          newForm.otherCity = userInfo.addresses_other?.city || "";
          newForm.otherState = userInfo.addresses_other?.state || "";
          newForm.otherZipCode = userInfo.addresses_other?.zipCode || "";
          newForm.otherCountry = userInfo.addresses_other?.country || "";
          // Emergency Contact
          newForm.emergencyContactName =
            userInfo.emergency_contact?.contactName || "";
          newForm.emergencyContactRelationship =
            userInfo.emergency_contact?.relationship || "";
          newForm.emergencyEmail = userInfo.emergency_contact?.email || "";
          newForm.emergencyPhone =
            userInfo.emergency_contact?.phoneNumber || "";
          newForm.personalWebsite = userInfo.personalWebsite || "";
          newForm.emergencyHobbies = userInfo.hobbies?.join(", ") || "";
          newForm.emergencyReligion = userInfo.religion || "";
          newForm.emergencyContactMethod =
            userInfo.preferredContactMethod || "";
          newForm.timezone = userInfo.timeZone || "";
          // Social Media
          newForm.linkedIn = userInfo.socialMedia?.linkedin?.url || "";
          newForm.twitter = userInfo.socialMedia?.twitter?.url || "";
          newForm.facebook = userInfo.socialMedia?.facebook?.url || "";
          newForm.instagram = userInfo.socialMedia?.instagram?.url || "";
          //  snapchat, whatsapp, telegram, signal, youtube, tiktok, twitch, discord, googleChat, Imessage, wechat, kik, slack, line, skype,
          newForm.snapchat = userInfo.socialMedia?.snapchat?.url || "";
          newForm.whatsapp = userInfo.socialMedia?.whatsapp?.url || "";
          newForm.telegram = userInfo.socialMedia?.telegram?.url || "";
          newForm.signal = userInfo.socialMedia?.signal?.url || "";
          newForm.youtube = userInfo.socialMedia?.youtube?.url || "";
          newForm.tiktok = userInfo.socialMedia?.tiktok?.url || "";
          newForm.twitch = userInfo.socialMedia?.twitch?.url || "";
          newForm.discord = userInfo.socialMedia?.discord?.url || "";
          newForm.googleChat = userInfo.socialMedia?.googleChat?.url || "";
          newForm.iMessage = userInfo.socialMedia?.iMessage?.url || "";
          newForm.wechat = userInfo.socialMedia?.wechat?.url || "";
          newForm.kik = userInfo.socialMedia?.kik?.url || "";
          newForm.slack = userInfo.socialMedia?.slack?.url || "";
          newForm.line = userInfo.socialMedia?.line?.url || "";
          newForm.skype = userInfo.socialMedia?.skype?.url || "";
          // Business Info
          newForm.jobTitle = userInfo?.business_details?.jobTitle || "";
          newForm.company = userInfo?.business_details?.company || "";
          newForm.department = userInfo?.business_details?.department || "";
          newForm.industry = userInfo?.business_details?.industry || "";
          newForm.workHours = userInfo?.business_details?.workHours || "";
          newForm.companyWebsite =
            userInfo?.business_details?.companyWebsite || "";
          newForm.businessLinkedIn =
            userInfo?.business_details?.businessLinkedIn || "";
          newForm.businessTwitter =
            userInfo?.business_details?.businessTwitter || "";
          newForm.companyLogo = userInfo?.business_details?.companyLogo || "";
          // Business Address
          newForm.officeBuilding =
            userInfo?.business_details?.business_address?.officeBuilding || "";
          newForm.businessStreet =
            userInfo?.business_details?.business_address?.street || "";
          newForm.businessCity =
            userInfo?.business_details?.business_address?.city || "";
          newForm.businessState =
            userInfo?.business_details?.business_address?.state || "";
          newForm.businessZipCode =
            userInfo?.business_details?.business_address?.zipCode || "";
          newForm.businessCountry =
            userInfo?.business_details?.business_address?.country || "";
          // Business Details
          newForm.workEmail = userInfo?.business_details?.workEmail || "";
          newForm.workPhone = userInfo?.business_details?.workPhone || "";
          newForm.workFax = userInfo?.business_details?.workFax || "";
          newForm.resume = userInfo?.business_details?.resume || "";
          newForm.certifications =
            userInfo?.business_details?.certifications || "";
          newForm.professionalNotes =
            userInfo?.business_details?.professionalNotes || "";
          // Checkboxes
          newForm.isSameBillingAddress = prevForm.isSameBillingAddress;
          newForm.isSameAddress = prevForm.isSameAddress;
          return newForm;
        });
        // Manage all emails and phones in state
        setAdditionalEmails(
          Array.isArray(userInfo.emails)
            ? userInfo.emails.slice(1).map((e) => e.address || "")
            : []
        );
        setAdditionalPhones(
          Array.isArray(userInfo.phoneNumbers)
            ? userInfo.phoneNumbers.slice(1).map((p) => p.number || "")
            : []
        );
      } catch (error) {
        console.error("Error setting profile data:", error);
        Alert.alert(
          "Error",
          "There was an error loading your profile data. Please try refreshing the page."
        );
      }
    } else if (ownProfileData) {
      try {
        const userInfo = ownProfileData;

        if (typeof userInfo !== "object")
          throw new Error("Invalid user info format");
        setForm((prevForm) => {
          const newForm = { ...prevForm };
          // Personal Details
          newForm.firstName = userInfo.firstName || "";
          newForm.middleName = userInfo.middleName || "";
          newForm.lastName = userInfo.lastName || "";
          newForm.nickname = userInfo.nickname || "";
          newForm.profileUrl = userInfo.profile_image || "";
          newForm.email = userInfo.emails?.[0]?.address || "";
          newForm.phone = userInfo.phoneNumbers?.[0]?.number || "";
          newForm.dateOfBirth = userInfo.dateOfBirth || "";
          newForm.gender = userInfo.gender || "";
          newForm.nationality = userInfo.nationality || "";
          // Health Insurance
          newForm.policyNumber = userInfo.healthInsurance?.policyNumber || "";
          newForm.insuranceProvider =
            userInfo.healthInsurance?.insuranceProvider || "";
          newForm.policyPeriod =
            userInfo.healthInsurance?.policyPeriod?.toString() || "";
          newForm.effectiveDate = userInfo.healthInsurance?.effectiveDate || "";
          newForm.expirationDate =
            userInfo.healthInsurance?.expirationDate || "";
          newForm.sumInsured =
            userInfo.healthInsurance?.sumInsured?.toString() || "";
          // Address
          newForm.street = userInfo.addresses_home?.street || "";
          newForm.city = userInfo.addresses_home?.city || "";
          newForm.state = userInfo.addresses_home?.state || "";
          newForm.zipCode = userInfo.addresses_home?.zipCode || "";
          newForm.country = userInfo.addresses_home?.country || "";
          newForm.apartment = userInfo.addresses_home?.apartment || "";
          // Billing Address
          newForm.billingApartment = userInfo.billing_address?.apartment || "";
          newForm.billingStreet = userInfo.billing_address?.street || "";
          newForm.billingCity = userInfo.billing_address?.city || "";
          newForm.billingState = userInfo.billing_address?.state || "";
          newForm.billingZipCode = userInfo.billing_address?.zipCode || "";
          newForm.billingCountry = userInfo.billing_address?.country || "";
          // Card Details
          newForm.cardName = userInfo.card_details?.nameOnCard || "";
          newForm.cardNumber = userInfo.card_details?.cardNumber || "";
          newForm.cardExpiry = userInfo.card_details?.expiryDate || "";
          newForm.cardCvv = userInfo.card_details?.cvv || "";
          // Bank Account
          newForm.accountName = userInfo.account_details?.name || "";
          newForm.accountNumber = userInfo.account_details?.accountNumber || "";
          newForm.ifscCode = userInfo.account_details?.ifscCode || "";
          newForm.bankName = userInfo.account_details?.bank || "";
          // Other Address
          newForm.otherApartment = userInfo.addresses_other?.apartment || "";
          newForm.otherStreet = userInfo.addresses_other?.street || "";
          newForm.otherCity = userInfo.addresses_other?.city || "";
          newForm.otherState = userInfo.addresses_other?.state || "";
          newForm.otherZipCode = userInfo.addresses_other?.zipCode || "";
          newForm.otherCountry = userInfo.addresses_other?.country || "";
          // Emergency Contact
          newForm.emergencyContactName =
            userInfo.emergency_contact?.contactName || "";
          newForm.emergencyContactRelationship =
            userInfo.emergency_contact?.relationship || "";
          newForm.emergencyEmail = userInfo.emergency_contact?.email || "";
          newForm.emergencyPhone =
            userInfo.emergency_contact?.phoneNumber || "";
          newForm.personalWebsite = userInfo.personalWebsite || "";
          newForm.emergencyHobbies = userInfo.hobbies?.join(", ") || "";
          newForm.emergencyReligion = userInfo.religion || "";
          newForm.emergencyContactMethod =
            userInfo.preferredContactMethod || "";
          newForm.timezone = userInfo.timeZone || "";
          // Social Media
          newForm.linkedIn = userInfo.socialMedia?.linkedin?.url || "";
          newForm.twitter = userInfo.socialMedia?.twitter?.url || "";
          newForm.facebook = userInfo.socialMedia?.facebook?.url || "";
          newForm.instagram = userInfo.socialMedia?.instagram?.url || "";
          //  snapchat, whatsapp, telegram, signal, youtube, tiktok, twitch, discord, googleChat, Imessage, wechat, kik, slack, line, skype,
          newForm.snapchat = userInfo.socialMedia?.snapchat?.url || "";
          newForm.whatsapp = userInfo.socialMedia?.whatsapp?.url || "";
          newForm.telegram = userInfo.socialMedia?.telegram?.url || "";
          newForm.signal = userInfo.socialMedia?.signal?.url || "";
          newForm.youtube = userInfo.socialMedia?.youtube?.url || "";
          newForm.tiktok = userInfo.socialMedia?.tiktok?.url || "";
          newForm.twitch = userInfo.socialMedia?.twitch?.url || "";
          newForm.discord = userInfo.socialMedia?.discord?.url || "";
          newForm.googleChat = userInfo.socialMedia?.googleChat?.url || "";
          newForm.iMessage = userInfo.socialMedia?.iMessage?.url || "";
          newForm.wechat = userInfo.socialMedia?.wechat?.url || "";
          newForm.kik = userInfo.socialMedia?.kik?.url || "";
          newForm.slack = userInfo.socialMedia?.slack?.url || "";
          newForm.line = userInfo.socialMedia?.line?.url || "";
          newForm.skype = userInfo.socialMedia?.skype?.url || "";
          // Business Info
          newForm.jobTitle = userInfo?.business_details?.jobTitle || "";
          newForm.company = userInfo?.business_details?.company || "";
          newForm.department = userInfo?.business_details?.department || "";
          newForm.industry = userInfo?.business_details?.industry || "";
          newForm.workHours = userInfo?.business_details?.workHours || "";
          newForm.companyWebsite =
            userInfo?.business_details?.companyWebsite || "";
          newForm.businessLinkedIn =
            userInfo?.business_details?.businessLinkedIn || "";
          newForm.businessTwitter =
            userInfo?.business_details?.businessTwitter || "";
          newForm.companyLogo = userInfo?.business_details?.companyLogo || "";
          // Business Address
          newForm.officeBuilding =
            userInfo?.business_details?.business_address?.officeBuilding || "";
          newForm.businessStreet =
            userInfo?.business_details?.business_address?.street || "";
          newForm.businessCity =
            userInfo?.business_details?.business_address?.city || "";
          newForm.businessState =
            userInfo?.business_details?.business_address?.state || "";
          newForm.businessZipCode =
            userInfo?.business_details?.business_address?.zipCode || "";
          newForm.businessCountry =
            userInfo?.business_details?.business_address?.country || "";
          // Business Details
          newForm.workEmail = userInfo?.business_details?.workEmail || "";
          newForm.workPhone = userInfo?.business_details?.workPhone || "";
          newForm.workFax = userInfo?.business_details?.workFax || "";
          newForm.resume = userInfo?.business_details?.resume || "";
          newForm.certifications =
            userInfo?.business_details?.certifications || "";
          newForm.professionalNotes =
            userInfo?.business_details?.professionalNotes || "";
          // Checkboxes
          newForm.isSameBillingAddress = prevForm.isSameBillingAddress;
          newForm.isSameAddress = prevForm.isSameAddress;

          return newForm;
        });
        // Manage all emails and phones in state
        setAdditionalEmails(
          Array.isArray(userInfo.emails)
            ? userInfo.emails.slice(1).map((e) => e.address || "")
            : []
        );
        setAdditionalPhones(
          Array.isArray(userInfo.phoneNumbers)
            ? userInfo.phoneNumbers.slice(1).map((p) => p.number || "")
            : []
        );
      } catch (error) {
        console.error("Error setting profile data:", error);
        Alert.alert(
          "Error",
          "There was an error loading your profile data. Please try refreshing the page."
        );
      }
    }
  }, [accessProfileData, accessProfileFromProfileIdState.error]);

  // Handlers
  const handleChange = useCallback(
    (field, value) => {
      setForm((prev) => {
        if (field.includes(".")) {
          const parts = field.split(".");
          const newForm = { ...prev };
          let current = newForm;
          for (let i = 0; i < parts.length - 1; i++) {
            current[parts[i]] = { ...current[parts[i]] };
            current = current[parts[i]];
          }
          current[parts[parts.length - 1]] = value;
          return newForm;
        }
        return { ...prev, [field]: value };
      });
      if (errors[field]) {
        setErrors((prev) => ({ ...prev, [field]: "" }));
      }
    },
    [errors]
  );

  const handleTabPress = useCallback(
    (sectionId, index) => {
      tabPosition.value = withSpring(index * styles.tabButton.width, {
        damping: 15,
        stiffness: 120,
        mass: 1.4,
      });
      setActiveMainSection(sectionId);
    },
    [tabPosition]
  );

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ translateX: tabPosition.value }],
  }));

  const collectVisibilitySettings = useCallback(
    () => ({
      ...(personalSectionRef.current?.getFieldVisibility() || {}),
      ...(socialSectionRef.current?.getFieldVisibility() || {}),
      ...(businessSectionRef.current?.getFieldVisibility() || {}),
    }),
    []
  );

  const handleNext = useCallback(async () => {
    try {
      setLoading(true);
      if (activeMainSection === "personal") {
        setActiveMainSection("social");
        tabPosition.value = withSpring(1 * styles.tabButton.width, {
          damping: 15,
          stiffness: 120,
          mass: 1.4,
        });
      } else if (activeMainSection === "social") {
        console.log("🚀 ~ handleNext ~ activeMainSection:", activeMainSection);

        // Build emails and phoneNumbers arrays
        const emails = [form.email, ...additionalEmails]
          .filter(Boolean)
          .map((address) => ({ address }));
        const phoneNumbers = [form.phone, ...additionalPhones]
          .filter(Boolean)
          .map((number) => ({ number }));
        const visibilitySettings = collectVisibilitySettings();
        const payload = {
          userInfo: {
            firstName: form.firstName,
            middleName: form.middleName,
            profile_image: form.profileUrl,
            lastName: form.lastName,
            nickname: form.nickname,
            dateOfBirth: form.dateOfBirth,
            gender: form.gender,
            nationality: form.nationality,
            maritalStatus: form.maritalStatus,
            spouseName: form.spouseName,
            emails,
            phoneNumbers,
            profilePicture: form.profilePicture,
            addresses_home: {
              apartment: form.apartment,
              street: form.street,
              city: form.city,
              state: form.state,
              postalCode: form.zipCode,
              country: form.country,
            },
            addresses_other: {
              sameAsHome: form.isSameAddress,
              apartment: form.otherApartment,
              street: form.otherStreet,
              city: form.otherCity,
              state: form.otherState,
              postalCode: form.otherZipCode,
              country: form.otherCountry,
            },
            personalWebsite: form.personalWebsite,
            hobbies:
              form.emergencyHobbies
                ?.split(",")
                .map((h) => h.trim())
                .filter((h) => h) || [],
            religion: form.emergencyReligion,
            preferredContactMethod: form.emergencyContactMethod,
            timeZone: form.timezone,
            emergency_contact: {
              contactName: form.emergencyContactName,
              phoneNumber: form.emergencyPhone,
              relationship: form.emergencyContactRelationship,
              email: form.emergencyEmail,
            },
            healthInsurance: {
              policyNumber: form.policyNumber,
              policyPeriod: form.policyPeriod,
              effectiveDate: form.effectiveDate,
              expirationDate: form.expirationDate,
              sumInsured: form.sumInsured,
              insuranceProvider: form.insuranceProvider,
            },
            billing_address: {
              apartment: form.billingApartment,
              street: form.billingStreet,
              city: form.billingCity,
              state: form.billingState,
              postalCode: form.billingZipCode,
              country: form.billingCountry,
            },
            card_details: {
              nameOnCard: form.cardName,
              cardNumber: form.cardNumber,
              expiryDate: form.cardExpiry,
              cvv: form.cardCvv,
            },
            account_details: {
              name: form.accountName,
              bank: form.bankName,
              accountNumber: form.accountNumber,
              ifscCode: form.ifscCode,
            },
            socialMedia: {
              facebook: { url: form.facebook || "" },
              instagram: { url: form.instagram || "" },
              twitter: { url: form.twitter || "" },
              linkedin: { url: form.linkedIn || "" },
              snapchat: { url: form.snapchat || "" },
              whatsapp: { url: form.whatsapp || "" },
              telegram: { url: form.telegram || "" },
              signal: { url: form.signal || "" },
              skype: { url: form.skype || "" },
              youtube: { url: form.youtube || "" },
              twitch: { url: form.twitch || "" },
              tiktok: { url: form.tiktok || "" },
              iMessage: { url: form.iMessage || "" },
              googleChat: { url: form.googleChat || "" },
              discord: { url: form.discord || "" },
              wechat: { url: form.wechat || "" },
              kik: { url: form.kik || "" },
              slack: { url: form.slack || "" },
              line: { url: form.line || "" },
            },
          },
          getProfileAccess: visibilitySettings,
        };
        console.log(
          "🚀 ~ handleNext ~ payload:",
          JSON.stringify(payload, null, 2)
        );

        const profile_management_id = profileId;
        const response = await dispatch(
          updateSharingProfile({
            profile_management_id,
            profileData: payload,
          })
        );

        if (response.payload && response.payload.success) {
          showToast("success", "Profile submitted.");
          setActiveMainSection("business");
          tabPosition.value = withSpring(2 * styles.tabButton.width, {
            damping: 15,
            stiffness: 120,
            mass: 1.4,
          });
        } else {
          Alert.alert("Error", "Failed to save social information");
        }
      } else if (activeMainSection === "business") {
        const visibilitySettings = collectVisibilitySettings();
        // Construct the business details payload as per user requirements
        const businessPayload = {
          professionalInfo: {
            account_details: {
              name: form.accountName || "",
              bank: form.bankName || "",
              accountNumber: form.accountNumber || "",
              ifscCode: form.ifscCode || "",
              paypalEmail: form.paypalEmail || "",
            },
            billing_address: {
              officeBuilding: form.billingApartment || "",
              street: form.billingStreet || "",
              city: form.billingCity || "",
              state: form.billingState || "",
              postalCode: form.billingZipCode || "",
              country: form.billingCountry || "",
            },
            business_address: {
              officeBuilding: form.officeBuilding || "",
              street: form.businessStreet || "",
              city: form.businessCity || "",
              state: form.businessState || "",
              postalCode: form.businessZipCode || "",
              country: form.businessCountry || "",
            },
            card_details: {
              nameOnCard: form.cardName || "",
              cardNumber: form.cardNumber || "",
              expiryDate: form.cardExpiry || "",
              cvv: form.cardCvv || "",
            },
            certifications: Array.isArray(form.certifications)
              ? form.certifications
              : form.certifications
              ? [{ url: form.certifications, _id: "" }]
              : [],
            companyInformation: {
              company_name: form.company || "",
              company_logo: form.companyLogo || "",
              email: form.workEmail || "",
              phone: form.workPhone || "",
              fax: form.workFax || "",
              website: form.companyWebsite || "",
            },
            companyMessengerIds: {
              iMessage: { url: form.iMessage || "" },
              googleChat: { url: form.googleChat || "" },
              discord: { url: form.discord || "" },
              slack: { url: form.slack || "" },
              wechat: { url: form.wechat || "" },
              kik: { url: form.kik || "" },
              line: { url: form.line || "" },
            },
            companySocialMedia: {
              facebook: { url: form.facebook || "" },
              instagram: { url: form.instagram || "" },
              twitter: { url: form.twitter || "" },
              linkedin: { url: form.linkedIn || "" },
              snapchat: { url: form.snapchat || "" },
              whatsapp: { url: form.whatsapp || "" },
              telegram: { url: form.telegram || "" },
              signal: { url: form.signal || "" },
              skype: { url: form.skype || "" },
              youtube: { url: form.youtube || "" },
              twitch: { url: form.twitch || "" },
              tiktok: { url: form.tiktok || "" },
            },
            contactId: user?._id || "",
            department: form.department || "",
            industry: form.industry || "",
            jobTitle: form.jobTitle || "",
            notes: form.professionalNotes || "",
            officeLocation: form.officeBuilding || "",
            resume: form.resume || "",
            status: "active",
            userId: user?._id || "",
            workContact: {
              phoneNumber: form.workPhone || "",
              fax: form.workFax || "",
              email: form.workEmail || "",
            },
            workSchedule: form.workHours || "",
            xProfile: form.businessTwitter || "",
          },
          privacySettings: visibilitySettings,
        };
        console.log("Business Profile Data:", JSON.stringify(businessPayload));

        const profile_management_id = user?._id;
        const response = await dispatch(
          updateBusinessDetailsSharingProfile({
            profile_management_id,
            profileData: businessPayload,
          })
        );
        if (response.payload && response.payload.success) {
          Alert.alert("Success", "Profile completed successfully");
          navigation.navigate("MainApp");
        } else {
          Alert.alert("Error", "Failed to complete profile");
        }
      }
    } catch (error) {
      console.error("Error in profile completion:", error);
      Alert.alert("Error", "An unexpected error occurred");
    } finally {
      setLoading(false);
    }
  }, [
    activeMainSection,
    collectVisibilitySettings,
    dispatch,
    form,
    navigation,
    tabPosition,
    user,
  ]);

  return (
    <View style={styles.container}>
      <Header
        title={ownProfileData ? "Edit your Profile" : "Complete Your Profile"}
        textCenter
        leftIcon={icons.backButton}
        onPressLeft={() => navigation.goBack()}
        pb={15}
      />
      <TabNavigation
        activeSection={activeMainSection}
        tabPosition={animatedStyle}
        handleTabPress={handleTabPress}
        mainSections={MAIN_SECTIONS}
      />
      <KeyboardAvoidingView
        style={{ flex: 1 }}
        behavior={Platform.OS === "ios" ? "height" : null}
        keyboardVerticalOffset={0}
      >
        <ScrollView
          ref={scrollViewRef}
          contentContainerStyle={[
            styles.formContainer,
            isTimezoneDropdownOpen && { paddingBottom: 200 },
          ]}
          showsVerticalScrollIndicator={false}
        >
          <ProfilePicture
            uri={form.profileUrl}
            onUploadSuccess={(url) =>
              setForm((prev) => ({ ...prev, profileUrl: url }))
            }
          />
          {activeMainSection === "personal" && (
            <PersonalSection
              ref={personalSectionRef}
              form={form}
              handleChange={handleChange}
              errors={errors}
              setErrors={setErrors}
              phoneInputRef={phoneInputRef}
              ownProfileData={ownProfileData}
              onTimezoneDropdownToggle={setIsTimezoneDropdownOpen}
              additionalEmails={additionalEmails}
              setAdditionalEmails={setAdditionalEmails}
              additionalPhones={additionalPhones}
              setAdditionalPhones={setAdditionalPhones}
            />
          )}
          {activeMainSection === "social" && (
            <SocialSection
              ref={socialSectionRef}
              form={form}
              handleChange={handleChange}
              errors={errors}
            />
          )}
          {activeMainSection === "business" && (
            <BusinessSection
              ref={businessSectionRef}
              form={form}
              handleChange={handleChange}
              errors={errors}
            />
          )}
          <PrimaryButton
            onPress={handleNext}
            title="Next"
            style={[styles.nextButton]}
            loading={loading}
          />
        </ScrollView>
      </KeyboardAvoidingView>
    </View>
  );
};

ProfileCompletionScreen.propTypes = {
  navigation: PropTypes.shape({
    navigate: PropTypes.func.isRequired,
    goBack: PropTypes.func.isRequired,
  }).isRequired,
};

export default ProfileCompletionScreen;
