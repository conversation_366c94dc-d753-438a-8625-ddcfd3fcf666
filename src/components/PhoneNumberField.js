import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Image,
} from "react-native";
import CountryPicker from "react-native-country-picker-modal";
import { useTranslation } from "react-i18next";
import PropTypes from "prop-types";
import colors from "../assets/colors";
import MyText from "./MyText";
import commonStyles from "../assets/commonStyles";
import { maxLengthsByCountry } from "../utils/maxLengthwCountryCode";
import icons from "../assets/icons";

const PhoneNumberField = ({
  value,
  phoneInputRef,
  onChangeFormatted,
  onChangeRaw,
  onCountryCodeChange,
  onCodeChange,
  error,
  setError,
  disabled,
  label,
  containerStyle,
  countryCodeProp = "QA",
  callingCodeProp = "974",
  placeholderText = "Enter mobile",
  showVisibilityToggle = false,
  isVisible = false,
  onVisibilityToggle,
  labelStyle,
  labelP = false,
  labelMedium = false,
}) => {
  // Defensive fallback for countryCodeProp and callingCodeProp
  const safeCountryCode = countryCodeProp || "IN";
  const safeCallingCode = callingCodeProp || "91";

  const [selectedCountryCode, setSelectedCountryCode] =
    useState(safeCountryCode);
  const [callingCode, setCallingCode] = useState(safeCallingCode);
  const [isPickerVisible, setIsPickerVisible] = useState(false);
  const { t } = useTranslation();

  // Update local state when props change, but only on initial mount or explicit prop changes
  useEffect(() => {
    // Only update if the props are different from current state to avoid loops
    if (safeCountryCode !== selectedCountryCode) {
      setSelectedCountryCode(safeCountryCode);
    }
    if (safeCallingCode !== callingCode) {
      setCallingCode(safeCallingCode);
    }
  }, [safeCountryCode, safeCallingCode]);

  // This effect notifies parent component of changes initiated from this component
  useEffect(() => {
    // Only call parent callbacks if the change originated from this component
    // (not from prop changes)
    console.log("Updated callingCode:", callingCode);
    console.log("Updated selectedCountryCode:", selectedCountryCode);

    // Avoid unnecessary updates to parent state
    if (callingCode !== safeCallingCode && onCodeChange) {
      console.log("Calling onCodeChange with:", callingCode);
      onCodeChange(callingCode);
    }

    if (selectedCountryCode !== safeCountryCode && onCountryCodeChange) {
      console.log("Calling onCountryCodeChange with:", selectedCountryCode);
      onCountryCodeChange(selectedCountryCode);
    }
  }, [callingCode, selectedCountryCode, safeCallingCode, safeCountryCode]);

  const handleCountrySelect = (country) => {
    console.log("Country selected:", country);
    const newCountryCode = country.cca2;
    const newCallingCode = country.callingCode[0];

    console.log(
      `Selected new country: ${newCountryCode} with calling code: ${newCallingCode}`
    );

    // Update local state first
    // Clear the number input when changing the country code
    if (onChangeRaw) {
      onChangeRaw("");
    }
    setSelectedCountryCode(newCountryCode);
    setCallingCode(newCallingCode);

    // The useEffect will handle notifying the parent component
    // This avoids duplicate calls and potential race conditions
  };

  const openCountryPicker = () => {
    setIsPickerVisible(true);
  };

  const handleChangeText = (text) => {
    if (onChangeRaw) {
      onChangeRaw(text);
    }
    // Maintain existing validation logic
    if (phoneInputRef?.current?.isValidNumber && setError) {
      const isValid = phoneInputRef.current.isValidNumber(text);
      setError(isValid ? "" : "Please enter a valid phone number");
    }
  };

  return (
    <View style={[styles.container, containerStyle]}>
      {label && (
        <MyText
          p={labelP}
          medium={labelMedium}
          style={[styles.label, labelStyle]}
        >
          {label}
        </MyText>
      )}
      <View style={commonStyles.row}>
        <TouchableOpacity
          activeOpacity={0.8}
          style={[
            styles.countryPicker,
            error && styles.errorBorder,
            disabled && styles.disabled,
            { zIndex: 999 },
          ]}
          onPress={openCountryPicker}
          disabled={true}
        >
          <View style={styles.countryPickerContent}>
            <CountryPicker
              countryCode={selectedCountryCode}
              withCallingCode
              withFlag
              // disableNativeModal={disabled}
              withFilter
              withEmoji={true}
              onSelect={handleCountrySelect}
              visible={isPickerVisible}
              onClose={() => setIsPickerVisible(false)}
            />
            <MyText p>+{callingCode}</MyText>
          </View>
        </TouchableOpacity>

        <View style={{ position: "relative", flex: 1, marginLeft: 10 }}>
          <TextInput
            ref={phoneInputRef}
            value={value}
            onChangeText={handleChangeText}
            keyboardType="numeric"
            editable={!disabled}
            maxLength={maxLengthsByCountry[selectedCountryCode]}
            style={[
              styles.input,
              commonStyles?.text,
              error && styles.errorBorder,
              disabled && styles.disabled,
              {
                textAlign: "left",
              },
              showVisibilityToggle && { paddingRight: 40 }, // Add padding for the eye icon
            ]}
            placeholderTextColor={colors.gray1}
            placeholder={placeholderText}
          />

          {/* Visibility Toggle */}
          {showVisibilityToggle && (
            <TouchableOpacity
              style={styles.visibilityToggle}
              onPress={onVisibilityToggle}
              activeOpacity={0.7}
              disabled={disabled}
            >
              <Image
                source={
                  isVisible
                    ? icons.openEyeProfileIcon
                    : icons.closedEyeProfileIcon
                }
                style={styles.visibilityIcon}
                resizeMode="contain"
              />
            </TouchableOpacity>
          )}
        </View>
      </View>
      {error && <Text style={styles.errorText}>{error}</Text>}
    </View>
  );
};

PhoneNumberField.propTypes = {
  value: PropTypes.string,
  phoneInputRef: PropTypes.shape({
    current: PropTypes.shape({
      isValidNumber: PropTypes.func,
    }),
  }),
  onChangeFormatted: PropTypes.func,
  onChangeRaw: PropTypes.func,
  onCountryCodeChange: PropTypes.func,
  onCodeChange: PropTypes.func,
  error: PropTypes.string,
  setError: PropTypes.func,
  disabled: PropTypes.bool,
  label: PropTypes.string,
  containerStyle: PropTypes.object,
  countryCodeProp: PropTypes.string,
  callingCodeProp: PropTypes.string,
  placeholderText: PropTypes.string,
  showVisibilityToggle: PropTypes.bool,
  isVisible: PropTypes.bool,
  onVisibilityToggle: PropTypes.func,
  labelStyle: PropTypes.object,
  labelP: PropTypes.bool,
  labelMedium: PropTypes.bool,
};

PhoneNumberField.defaultProps = {
  countryCodeProp: "QA",
  callingCodeProp: "974",
  disabled: false,
};

const styles = StyleSheet.create({
  container: {
    width: "100%",
    marginBottom: 20,
  },
  labelContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 6,
  },
  label: {
    flex: 1,
    textAlign: "left",
  },

  input: {
    flex: 1,
    height: 48,
    padding: 12,
    alignItems: "center",
    backgroundColor: "#f2f2f2",
    borderRadius: 12,
    paddingHorizontal: 12,
  },
  countryPicker: {
    paddingVertical: 6,
    paddingHorizontal: 10,
    height: 48,
    alignItems: "center",
    backgroundColor: "#f2f2f2",
    borderRadius: 12,
    justifyContent: "center",
  },
  countryPickerContent: {
    flexDirection: "row",
    alignItems: "center",
  },
  errorBorder: {
    borderColor: colors.red,
  },
  errorText: {
    color: colors.red,
    fontSize: 12,
    marginTop: 4,
  },
  disabled: {
    backgroundColor: colors.lightGray,
    opacity: 0.7,
  },
  visibilityToggle: {
    position: "absolute",
    right: 12,
    top: "50%",
    transform: [{ translateY: -10 }],
    padding: 5,
    zIndex: 1,
  },
  visibilityIcon: {
    width: 20,
    height: 20,
  },
});

export default PhoneNumberField;
